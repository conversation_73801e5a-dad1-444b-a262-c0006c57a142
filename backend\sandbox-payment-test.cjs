const axios = require('axios');
const { spawn } = require('child_process');

const BASE_URL = 'http://localhost:8080/api/v1';

// 创建axios实例，支持cookie
const apiClient = axios.create({
  baseURL: BASE_URL,
  withCredentials: true,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

class SandboxPaymentTester {
  constructor() {
    this.orderId = null;
    this.cookies = null;
  }

  async login() {
    console.log('🔐 登录系统...');
    try {
      const loginResponse = await apiClient.post('/auth/login', {
        email: '<EMAIL>',
        password: 'qaz123'
      });

      console.log('✅ 登录成功');
      console.log('   用户:', loginResponse.data.user.email);
      
      // 提取并设置cookie
      const cookies = loginResponse.headers['set-cookie'];
      if (cookies) {
        const accessTokenCookie = cookies.find(cookie => cookie.startsWith('access_token='));
        if (accessTokenCookie) {
          this.cookies = accessTokenCookie.split(';')[0];
          apiClient.defaults.headers.Cookie = this.cookies;
          console.log('   ✅ 认证Cookie已设置');
        }
      }
      
      return true;
    } catch (error) {
      console.error('❌ 登录失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async createOrder(paymentMethod = 'ALIPAY') {
    console.log(`\n💳 创建${paymentMethod}订单...`);
    try {
      const orderResponse = await apiClient.post('/orders', {
        planId: 'cmcu3yg3w0000bcs1eqdlxngz',
        billingCycle: 'MONTHLY',
        paymentMethod: paymentMethod
      });

      this.orderId = orderResponse.data.order.id;
      console.log('✅ 订单创建成功');
      console.log('   订单ID:', this.orderId);
      console.log('   订单状态:', orderResponse.data.order.status);
      console.log('   订单金额:', orderResponse.data.order.amount);
      console.log('   支付方式:', orderResponse.data.order.paymentMethod);
      
      // 检查是否有支付二维码
      if (orderResponse.data.paymentDetails?.qrCode) {
        console.log('   ✅ 支付二维码已生成');
        console.log('   二维码内容:', orderResponse.data.paymentDetails.qrCode.substring(0, 50) + '...');
      } else {
        console.log('   ⚠️ 未生成支付二维码');
      }
      
      return orderResponse.data;
    } catch (error) {
      console.error('❌ 订单创建失败:', error.response?.data?.message || error.message);
      if (error.response?.data?.errors) {
        console.error('   验证错误:', JSON.stringify(error.response.data.errors, null, 2));
      }
      return null;
    }
  }

  async simulatePaymentCallback(paymentMethod = 'ALIPAY') {
    if (!this.orderId) {
      console.error('❌ 没有订单ID，无法模拟支付回调');
      return false;
    }

    console.log(`\n🔄 模拟${paymentMethod}支付成功回调...`);
    
    try {
      let response;
      
      if (paymentMethod === 'ALIPAY') {
        // 支付宝回调参数
        const params = new URLSearchParams({
          app_id: '9021000122671080',
          auth_app_id: '9021000122671080',
          buyer_id: '2088102177846875',
          buyer_logon_id: 'csq***@sandbox.com',
          buyer_pay_amount: '0.01',
          charset: 'utf-8',
          gmt_create: new Date().toISOString().replace('T', ' ').substring(0, 19),
          gmt_payment: new Date().toISOString().replace('T', ' ').substring(0, 19),
          notify_id: `notify_${Date.now()}`,
          notify_time: new Date().toISOString().replace('T', ' ').substring(0, 19),
          notify_type: 'trade_status_sync',
          out_trade_no: this.orderId,
          receipt_amount: '0.01',
          seller_email: '<EMAIL>',
          seller_id: '2088102177649450',
          subject: `Membership Subscription - ${this.orderId}`,
          total_amount: '0.01',
          trade_no: `2025070822001446870${Math.floor(Math.random() * 1000000)}`,
          trade_status: 'TRADE_SUCCESS',
          version: '1.0',
          sign_type: 'RSA2',
          sign: 'mock_signature_for_testing'
        });

        response = await axios.post(
          `${BASE_URL}/payment/notify/alipay`,
          params.toString(),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        );
      } else if (paymentMethod === 'WECHAT') {
        // 微信支付回调参数
        const notifyData = {
          id: `notify_${Date.now()}`,
          create_time: new Date().toISOString(),
          event_type: 'TRANSACTION.SUCCESS',
          resource_type: 'encrypt-resource',
          resource: {
            original_type: 'transaction',
            algorithm: 'AEAD_AES_256_GCM',
            ciphertext: JSON.stringify({
              out_trade_no: this.orderId,
              transaction_id: `wx${Date.now()}${Math.floor(Math.random() * 1000)}`,
              trade_type: 'NATIVE',
              trade_state: 'SUCCESS',
              trade_state_desc: '支付成功',
              bank_type: 'OTHERS',
              success_time: new Date().toISOString(),
              payer: {
                openid: 'mock_openid_123456'
              },
              amount: {
                total: 1, // 微信支付使用分，0.01元=1分
                payer_total: 1,
                currency: 'CNY',
                payer_currency: 'CNY'
              }
            }),
            associated_data: 'transaction',
            nonce: 'mock_nonce'
          },
          summary: '支付成功'
        };

        response = await axios.post(
          `${BASE_URL}/payment/notify/wechat`,
          notifyData,
          {
            headers: {
              'Content-Type': 'application/json',
              'Wechatpay-Signature': 'mock_signature',
              'Wechatpay-Timestamp': Math.floor(Date.now() / 1000).toString(),
              'Wechatpay-Nonce': 'mock_nonce',
              'Wechatpay-Serial': '5157F09EFDC096DE15EBE81A47057A7232F1B8E1',
            },
          }
        );
      }

      console.log('✅ 支付回调成功:', response.status, response.data);
      return true;
    } catch (error) {
      console.error('❌ 支付回调失败:', error.response?.data || error.message);
      return false;
    }
  }

  async checkOrderStatus() {
    if (!this.orderId) {
      console.error('❌ 没有订单ID，无法检查状态');
      return null;
    }

    console.log('\n📊 检查订单状态...');
    try {
      const response = await apiClient.get(`/orders/${this.orderId}`);
      console.log('✅ 订单状态查询成功');
      console.log('   当前状态:', response.data.status);
      console.log('   订单金额:', response.data.amount);
      console.log('   创建时间:', response.data.createdAt);
      console.log('   更新时间:', response.data.updatedAt);
      return response.data;
    } catch (error) {
      console.error('❌ 订单状态查询失败:', error.response?.data?.message || error.message);
      return null;
    }
  }

  async runCompleteTest(paymentMethod = 'ALIPAY') {
    console.log('🚀 开始完整沙箱支付测试...');
    console.log(`💳 支付方式: ${paymentMethod}`);
    console.log('─'.repeat(60));

    // 1. 登录
    const loginSuccess = await this.login();
    if (!loginSuccess) {
      console.log('❌ 测试终止：登录失败');
      return;
    }

    // 2. 创建订单
    const orderData = await this.createOrder(paymentMethod);
    if (!orderData) {
      console.log('❌ 测试终止：订单创建失败');
      return;
    }

    // 3. 检查初始订单状态
    const initialOrder = await this.checkOrderStatus();
    if (!initialOrder || initialOrder.status !== 'PENDING') {
      console.log('❌ 测试终止：订单状态异常');
      return;
    }

    // 4. 模拟支付成功
    const paymentSuccess = await this.simulatePaymentCallback(paymentMethod);
    if (!paymentSuccess) {
      console.log('❌ 支付回调失败');
    }

    // 5. 等待处理
    console.log('\n⏳ 等待支付回调处理...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 6. 检查最终订单状态
    const finalOrder = await this.checkOrderStatus();

    // 7. 测试结果
    console.log('\n🎉 测试完成!');
    console.log('─'.repeat(60));
    console.log('📊 测试结果:');
    console.log(`   订单ID: ${this.orderId}`);
    console.log(`   支付方式: ${paymentMethod}`);
    console.log(`   初始状态: ${initialOrder?.status || '未知'}`);
    console.log(`   最终状态: ${finalOrder?.status || '未知'}`);
    console.log(`   支付回调: ${paymentSuccess ? '成功' : '失败'}`);
    console.log(`   状态更新: ${finalOrder?.status === 'PAID' ? '成功' : '失败'}`);

    return {
      orderId: this.orderId,
      paymentMethod,
      initialStatus: initialOrder?.status,
      finalStatus: finalOrder?.status,
      paymentCallbackSuccess: paymentSuccess,
      statusUpdated: finalOrder?.status === 'PAID'
    };
  }
}

// 运行测试
async function runTests() {
  const tester = new SandboxPaymentTester();
  
  // 测试支付宝
  console.log('🔵 测试支付宝支付流程');
  const alipayResult = await tester.runCompleteTest('ALIPAY');
  
  // 等待一段时间
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试微信支付
  console.log('\n🟢 测试微信支付流程');
  const wechatTester = new SandboxPaymentTester();
  const wechatResult = await wechatTester.runCompleteTest('WECHAT');
  
  // 总结
  console.log('\n📋 总测试结果:');
  console.log('─'.repeat(60));
  console.log('支付宝测试:', alipayResult?.statusUpdated ? '✅ 成功' : '❌ 失败');
  console.log('微信支付测试:', wechatResult?.statusUpdated ? '✅ 成功' : '❌ 失败');
}

runTests();
